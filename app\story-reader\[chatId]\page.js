'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { auth } from '@/lib/firebase';
import { ThemedButton } from '@/app/theme/ThemeProvider';
import AudioPlayer from '@/app/components/AudioPlayer';
import { ArrowLeft, Volume2, VolumeX, BookOpen, TreePine, ChevronRight, RotateCcw, MapPin, Home, Star } from 'lucide-react';

export default function StoryReaderPage() {
  const params = useParams();
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);
  
  const [storyData, setStoryData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [currentScenario, setCurrentScenario] = useState('root');
  const [storyText, setStoryText] = useState('');
  const [scenarioTree, setScenarioTree] = useState({});
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);
  const [navigationHistory, setNavigationHistory] = useState(['root']);
  const [visitedScenarios, setVisitedScenarios] = useState(new Set(['root']));

  const chatId = params?.chatId;

  // Function to parse story text into tree structure
  const parseStoryIntoTree = (text) => {
    const tree = {};
    
    // Split by major headings (marked with **)
    const sections = text.split(/\*\*(.*?)\*\*/g);
    
    let currentKey = 'root';
    let currentContent = '';
    
    for (let i = 0; i < sections.length; i++) {
      const section = sections[i].trim();
      if (!section) continue;
      
      // If this is a heading (odd indices after split)
      if (i % 2 === 1) {
        // Save previous section
        if (currentContent) {
          if (!tree[currentKey]) {
            tree[currentKey] = { content: '', options: [] };
          }
          tree[currentKey].content = currentContent.trim();
        }
        
        // Start new section
        currentKey = section.toLowerCase().replace(/[^a-z0-9]/g, '');
        currentContent = '';
      } else {
        currentContent += section;
      }
    }
    
    // Save the last section
    if (currentContent) {
      if (!tree[currentKey]) {
        tree[currentKey] = { content: '', options: [] };
      }
      tree[currentKey].content = currentContent.trim();
    }
    
    // If no structured content found, create a simple root
    if (Object.keys(tree).length === 0) {
      tree.root = {
        content: text,
        options: []
      };
    }
    
    // Auto-generate options based on scenario names
    const scenarioKeys = Object.keys(tree);
    scenarioKeys.forEach(key => {
      if (key === 'root') {
        // Root scenario gets main scenario options
        const mainScenarios = scenarioKeys.filter(k => 
          k !== 'root' && 
          !k.includes('a') && 
          !k.includes('b') && 
          !k.includes('c') && 
          !k.includes('d') &&
          k.match(/^scenario\d+$/)
        );
        tree.root.options = mainScenarios.map(scenario => ({
          id: scenario,
          text: `Go to ${scenario.replace('scenario', 'Scenario ')}`,
          target: scenario
        }));
      } else if (key.match(/^scenario\d+$/)) {
        // Main scenarios get their sub-options
        const subScenarios = scenarioKeys.filter(k => k.startsWith(key) && k !== key);
        tree[key].options = subScenarios.map(scenario => ({
          id: scenario,
          text: `Choose ${scenario.replace(key, '').toUpperCase()}`,
          target: scenario
        }));
      }
    });
    
    return tree;
  };

  // Check authentication state
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      setUser(currentUser);
      setAuthLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Fetch story data from Firestore
  useEffect(() => {
    const fetchStoryData = async () => {
      if (!chatId) {
        setFetchError('No chat ID provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch(`/api/chats/${chatId}/storyData`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setFetchError('Story not found');
          } else {
            setFetchError('Failed to load story data');
          }
          setIsLoading(false);
          return;
        }

        const data = await response.json();
        setStoryData(data);
        setFetchError(null);

        // Process the story format
        if (data.storyText) {
          setStoryText(data.storyText);
          const tree = parseStoryIntoTree(data.storyText);
          setScenarioTree(tree);
        }
      } catch (error) {
        console.error('Error fetching story data:', error);
        setFetchError('Failed to load story data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStoryData();
  }, [chatId]);

  // Navigate to a scenario
  const navigateToScenario = (scenarioId) => {
    if (scenarioTree[scenarioId]) {
      setCurrentScenario(scenarioId);
      setNavigationHistory(prev => [...prev, scenarioId]);
      setVisitedScenarios(prev => new Set(prev).add(scenarioId));
    }
  };

  // Go back to previous scenario
  const goBack = () => {
    if (navigationHistory.length > 1) {
      const newHistory = [...navigationHistory];
      newHistory.pop(); // Remove current
      const previousScenario = newHistory[newHistory.length - 1];
      setCurrentScenario(previousScenario);
      setNavigationHistory(newHistory);
    }
  };

  // Reset to root
  const resetToRoot = () => {
    setCurrentScenario('root');
    setNavigationHistory(['root']);
  };

  // Get current scenario content for TTS
  const getCurrentScenarioText = () => {
    const scenario = scenarioTree[currentScenario];
    if (scenario) {
      return `${getScenarioTitle(currentScenario)}. ${scenario.content}`;
    }
    return storyText;
  };

  // Get readable scenario title
  const getScenarioTitle = (scenarioId) => {
    if (scenarioId === 'root') return 'Opening Scenario';
    return scenarioId.replace(/([a-z])(\d)/g, '$1 $2')
                   .replace(/scenario/i, 'Scenario')
                   .replace(/([a-z])([A-Z])/g, '$1 $2');
  };

  // Get breadcrumb path
  const getBreadcrumbs = () => {
    return navigationHistory.map(scenarioId => ({
      id: scenarioId,
      title: getScenarioTitle(scenarioId)
    }));
  };

  // Redirect to login if not authenticated
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-green-400 via-blue-400 to-purple-400">
        <div className="bg-white rounded-2xl p-8 shadow-2xl">
          <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-green-500 mx-auto"></div>
          <p className="text-center mt-4 text-gray-600 font-medium">Loading Story Tree...</p>
        </div>
      </div>
    );
  }

  if (!authLoading && !user) {
    router.push('/register');
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-green-400 via-blue-400 to-purple-400">
        <div className="bg-white rounded-2xl p-8 shadow-2xl text-center">
          <div className="animate-bounce mb-4">
            <TreePine className="w-16 h-16 text-green-500 mx-auto" />
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-t-4 border-b-4 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Building story branches...</p>
        </div>
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-400 via-pink-400 to-purple-400 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full text-center">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-3xl">🌳</span>
          </div>
          <h1 className="text-2xl font-bold text-red-600 mb-4">Story Tree Error</h1>
          <p className="text-gray-700 mb-6">{fetchError}</p>
          <div className="space-y-3">
            <button 
              onClick={() => window.location.reload()}
              className="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-xl transition-all transform hover:scale-105"
            >
              Try Again
            </button>
            <button 
              onClick={() => router.back()}
              className="w-full bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-xl transition-all transform hover:scale-105 flex items-center justify-center"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!storyData || (!storyData.storyText && (!storyData.questions || storyData.questions.length === 0))) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-400 via-blue-400 to-purple-400 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full text-center">
          <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <TreePine className="w-10 h-10 text-gray-400" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">No Story Tree Found</h1>
          <p className="text-gray-600 mb-6">This story adventure doesn't have any branches yet.</p>
          <button 
            onClick={() => router.back()}
            className="w-full bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-6 rounded-xl transition-all transform hover:scale-105 flex items-center justify-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const currentScenarioData = scenarioTree[currentScenario];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-400 via-blue-400 to-purple-400">
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-2xl p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => router.back()}
              className="flex items-center bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-xl transition-all transform hover:scale-105"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Exit Story
            </button>

            <div className="text-center">
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-800 flex items-center justify-center">
                <TreePine className="w-8 h-8 mr-3 text-green-500" />
                Story Tree Adventure
              </h1>
            </div>

            <div className="flex items-center bg-green-100 px-3 py-2 rounded-xl">
              <MapPin className="w-5 h-5 text-green-600 mr-2" />
              <span className="text-green-700 font-bold">{visitedScenarios.size} Visited</span>
            </div>
          </div>

          {/* Breadcrumb Navigation */}
          <div className="flex items-center space-x-2 bg-gray-50 p-3 rounded-xl">
            <Home className="w-4 h-4 text-gray-500" />
            {getBreadcrumbs().map((crumb, index) => (
              <div key={crumb.id} className="flex items-center">
                {index > 0 && <ChevronRight className="w-4 h-4 text-gray-400 mx-1" />}
                <button
                  onClick={() => {
                    if (index < getBreadcrumbs().length - 1) {
                      setCurrentScenario(crumb.id);
                      setNavigationHistory(navigationHistory.slice(0, index + 1));
                    }
                  }}
                  className={`text-sm font-medium px-2 py-1 rounded-lg transition-colors ${
                    index === getBreadcrumbs().length - 1
                      ? 'bg-blue-500 text-white'
                      : 'text-blue-600 hover:bg-blue-100'
                  }`}
                >
                  {crumb.title}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Audio Control Panel */}
        <div className="bg-white rounded-2xl shadow-2xl p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-800 flex items-center">
              <Volume2 className="w-6 h-6 mr-2 text-blue-500" />
              Audio Player
            </h2>
            <button
              onClick={() => setIsAudioEnabled(!isAudioEnabled)}
              className={`flex items-center px-4 py-2 rounded-xl font-medium transition-all transform hover:scale-105 ${
                isAudioEnabled 
                  ? 'bg-red-500 hover:bg-red-600 text-white' 
                  : 'bg-green-500 hover:bg-green-600 text-white'
              }`}
            >
              {isAudioEnabled ? <VolumeX className="w-4 h-4 mr-2" /> : <Volume2 className="w-4 h-4 mr-2" />}
              {isAudioEnabled ? 'Disable Audio' : 'Enable Audio'}
            </button>
          </div>

          {isAudioEnabled && (
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-xl">
              <AudioPlayer
                text={getCurrentScenarioText()}
                voiceGender="FEMALE"
              />
            </div>
          )}
        </div>

        {/* Current Scenario */}
        <div className="bg-white rounded-2xl shadow-2xl p-6 lg:p-8 mb-6">
          {/* Scenario Header */}
          <div className="text-center mb-6">
            <div className="inline-flex items-center bg-gradient-to-r from-green-100 to-blue-100 px-4 py-2 rounded-full shadow-md mb-4">
              <Star className="w-5 h-5 text-green-600 mr-2" />
              <span className="font-bold text-gray-700">{getScenarioTitle(currentScenario)}</span>
            </div>
          </div>

          {/* Scenario Content */}
          <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl p-6 mb-6">
            <div className="bg-white rounded-xl p-6 shadow-md">
              <div className="text-gray-700 leading-relaxed text-lg whitespace-pre-wrap">
                {currentScenarioData?.content || 'Loading scenario...'}
              </div>
            </div>
          </div>

          {/* Scenario Options */}
          {currentScenarioData?.options && currentScenarioData.options.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-xl font-bold text-gray-800 text-center mb-4">
                Choose Your Path
              </h3>
              <div className="grid gap-4 md:grid-cols-2">
                {currentScenarioData.options.map((option, index) => (
                  <button
                    key={option.id}
                    onClick={() => navigateToScenario(option.target)}
                    className={`p-4 rounded-xl text-left transition-all transform hover:scale-105 shadow-lg ${
                      visitedScenarios.has(option.target)
                        ? 'bg-gradient-to-r from-green-100 to-blue-100 border-2 border-green-300'
                        : 'bg-gradient-to-r from-purple-100 to-pink-100 hover:from-purple-200 hover:to-pink-200'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-bold text-gray-800 mb-2">
                          Option {String.fromCharCode(65 + index)}
                        </div>
                        <div className="text-gray-700">
                          {option.text}
                        </div>
                      </div>
                      <div className="flex items-center">
                        {visitedScenarios.has(option.target) && (
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                            <span className="text-white text-xs">✓</span>
                          </div>
                        )}
                        <ChevronRight className="w-5 h-5 text-gray-500" />
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Navigation Controls */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t">
            <button
              onClick={goBack}
              disabled={navigationHistory.length <= 1}
              className="flex items-center bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed text-gray-700 font-medium py-3 px-6 rounded-xl transition-all transform hover:scale-105"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Go Back
            </button>

            <button
              onClick={resetToRoot}
              className="flex items-center bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl transition-all transform hover:scale-105 shadow-lg"
            >
              <RotateCcw className="w-5 h-5 mr-2" />
              Start Over
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center">
          <div className="bg-white rounded-2xl shadow-lg p-4">
            <p className="text-gray-600 font-medium flex items-center justify-center">
              <TreePine className="w-4 h-4 mr-2 text-green-500" />
              Navigate through the story tree by choosing your path!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}