'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { auth } from '@/lib/firebase';
import { ThemedButton } from '@/app/theme/ThemeProvider';
import AudioPlayer from '@/app/components/AudioPlayer';
import { ArrowLeft, Volume2, VolumeX, ChevronRight, RotateCcw, MapPin, Home } from 'lucide-react';

export default function StoryReaderPage() {
  const params = useParams();
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);
  
  const [storyData, setStoryData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [currentScenario, setCurrentScenario] = useState('root');
  const [storyText, setStoryText] = useState('');
  const [scenarioTree, setScenarioTree] = useState({});
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);
  const [navigationHistory, setNavigationHistory] = useState(['root']);
  const [visitedScenarios, setVisitedScenarios] = useState(new Set(['root']));

  const chatId = params?.chatId;

  // Function to parse story text into hierarchical tree structure
  const parseStoryIntoTree = (text) => {
    const tree = {};

    // Split by major headings (marked with **)
    const sections = text.split(/\*\*(.*?)\*\*/g);

    let currentKey = 'root';
    let currentContent = '';

    for (let i = 0; i < sections.length; i++) {
      const section = sections[i].trim();
      if (!section) continue;

      // If this is a heading (odd indices after split)
      if (i % 2 === 1) {
        // Save previous section
        if (currentContent) {
          if (!tree[currentKey]) {
            tree[currentKey] = { content: '', options: [], title: '' };
          }
          tree[currentKey].content = currentContent.trim();
        }

        // Extract scenario key and title
        const cleanSection = section.toLowerCase();
        let scenarioKey = 'root';
        let scenarioTitle = section;

        // Check for opening scenario
        if (cleanSection.includes('opening') || cleanSection.includes('start') || currentKey === 'root') {
          scenarioKey = 'root';
          scenarioTitle = section;
        }
        // Check for main scenarios (scenario1, scenario2, etc.)
        else if (cleanSection.match(/scenario\s*(\d+)/)) {
          const match = cleanSection.match(/scenario\s*(\d+)/);
          scenarioKey = `scenario${match[1]}`;
          scenarioTitle = section;
        }
        // Check for sub-scenarios (scenario1a, scenario1b, etc.)
        else if (cleanSection.match(/scenario\s*(\d+)\s*([a-z])/)) {
          const match = cleanSection.match(/scenario\s*(\d+)\s*([a-z])/);
          scenarioKey = `scenario${match[1]}${match[2]}`;
          scenarioTitle = section;
        }
        // Fallback: clean the section name
        else {
          scenarioKey = section.toLowerCase().replace(/[^a-z0-9]/g, '');
          scenarioTitle = section;
        }

        // Initialize the scenario
        if (!tree[scenarioKey]) {
          tree[scenarioKey] = { content: '', options: [], title: scenarioTitle };
        } else {
          tree[scenarioKey].title = scenarioTitle;
        }

        currentKey = scenarioKey;
        currentContent = '';
      } else {
        currentContent += section;
      }
    }

    // Save the last section
    if (currentContent) {
      if (!tree[currentKey]) {
        tree[currentKey] = { content: '', options: [], title: '' };
      }
      tree[currentKey].content = currentContent.trim();
    }

    // If no structured content found, create a simple root
    if (Object.keys(tree).length === 0) {
      tree.root = {
        content: text,
        options: [],
        title: 'Opening Scenario'
      };
    }

    // Generate hierarchical options based on scenario structure
    const scenarioKeys = Object.keys(tree);

    scenarioKeys.forEach(key => {
      if (key === 'root') {
        // Root scenario gets main scenario options (scenario1, scenario2, scenario3, scenario4)
        const mainScenarios = scenarioKeys
          .filter(k => k.match(/^scenario\d+$/))
          .sort((a, b) => {
            const numA = parseInt(a.replace('scenario', ''));
            const numB = parseInt(b.replace('scenario', ''));
            return numA - numB;
          });

        tree.root.options = mainScenarios.map(scenario => {
          const num = scenario.replace('scenario', '');
          const title = tree[scenario]?.title || `Scenario ${num}`;
          return {
            id: scenario,
            text: title,
            target: scenario
          };
        });
      }
      else if (key.match(/^scenario\d+$/)) {
        // Main scenarios get their sub-scenario options (scenario1a, scenario1b, etc.)
        const subScenarios = scenarioKeys
          .filter(k => k.startsWith(key) && k !== key && k.match(/^scenario\d+[a-z]$/))
          .sort((a, b) => {
            const letterA = a.slice(-1);
            const letterB = b.slice(-1);
            return letterA.localeCompare(letterB);
          });

        tree[key].options = subScenarios.map(scenario => {
          const letter = scenario.slice(-1).toUpperCase();
          const title = tree[scenario]?.title || `Option ${letter}`;
          return {
            id: scenario,
            text: title,
            target: scenario
          };
        });
      }
      // Sub-scenarios could potentially have further options (for deeper nesting)
      else if (key.match(/^scenario\d+[a-z]$/)) {
        // Look for deeper nested scenarios if they exist
        const deeperScenarios = scenarioKeys
          .filter(k => k.startsWith(key) && k !== key)
          .sort();

        if (deeperScenarios.length > 0) {
          tree[key].options = deeperScenarios.map(scenario => {
            const title = tree[scenario]?.title || scenario;
            return {
              id: scenario,
              text: title,
              target: scenario
            };
          });
        }
      }
    });

    return tree;
  };

  // Check authentication state
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      setUser(currentUser);
      setAuthLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Fetch story data from Firestore
  useEffect(() => {
    const fetchStoryData = async () => {
      if (!chatId) {
        setFetchError('No chat ID provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch(`/api/chats/${chatId}/storyData`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setFetchError('Story not found');
          } else {
            setFetchError('Failed to load story data');
          }
          setIsLoading(false);
          return;
        }

        const data = await response.json();
        setStoryData(data);
        setFetchError(null);

        // Process the story format
        if (data.storyText) {
          setStoryText(data.storyText);
          const tree = parseStoryIntoTree(data.storyText);
          setScenarioTree(tree);
        }
      } catch (error) {
        console.error('Error fetching story data:', error);
        setFetchError('Failed to load story data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStoryData();
  }, [chatId]);

  // Navigate to a scenario
  const navigateToScenario = (scenarioId) => {
    if (scenarioTree[scenarioId]) {
      setCurrentScenario(scenarioId);
      setNavigationHistory(prev => [...prev, scenarioId]);
      setVisitedScenarios(prev => new Set(prev).add(scenarioId));
    }
  };

  // Go back one level in the hierarchy
  const goBack = () => {
    if (navigationHistory.length > 1) {
      const newHistory = [...navigationHistory];
      newHistory.pop(); // Remove current
      const previousScenario = newHistory[newHistory.length - 1];
      setCurrentScenario(previousScenario);
      setNavigationHistory(newHistory);
    }
  };

  // Go up one level in the story hierarchy (not just previous visited)
  const goUpLevel = () => {
    const current = currentScenario;
    let parentScenario = 'root';

    // Determine parent based on scenario structure
    if (current.match(/^scenario\d+[a-z]$/)) {
      // Sub-scenario (e.g., scenario1a) goes back to main scenario (scenario1)
      parentScenario = current.slice(0, -1);
    } else if (current.match(/^scenario\d+$/)) {
      // Main scenario goes back to root
      parentScenario = 'root';
    }

    if (scenarioTree[parentScenario] && parentScenario !== current) {
      setCurrentScenario(parentScenario);
      // Update navigation history to reflect the hierarchical path
      const newHistory = navigationHistory.filter(id => {
        if (parentScenario === 'root') return id === 'root';
        if (parentScenario.match(/^scenario\d+$/)) {
          return id === 'root' || id === parentScenario;
        }
        return true;
      });
      if (!newHistory.includes(parentScenario)) {
        newHistory.push(parentScenario);
      }
      setNavigationHistory(newHistory);
    }
  };

  // Reset to root
  const resetToRoot = () => {
    setCurrentScenario('root');
    setNavigationHistory(['root']);
  };

  // Get current scenario content for TTS
  const getCurrentScenarioText = () => {
    const scenario = scenarioTree[currentScenario];
    if (scenario) {
      return `${getScenarioTitle(currentScenario)}. ${scenario.content}`;
    }
    return storyText;
  };

  // Get readable scenario title
  const getScenarioTitle = (scenarioId) => {
    // Use the title from the tree if available
    if (scenarioTree[scenarioId]?.title) {
      return scenarioTree[scenarioId].title;
    }

    // Fallback to formatted scenario names
    if (scenarioId === 'root') return 'Opening Scenario';

    if (scenarioId.match(/^scenario\d+$/)) {
      const num = scenarioId.replace('scenario', '');
      return `Scenario ${num}`;
    }

    if (scenarioId.match(/^scenario\d+[a-z]$/)) {
      const num = scenarioId.replace(/scenario(\d+)[a-z]/, '$1');
      const letter = scenarioId.slice(-1).toUpperCase();
      return `Scenario ${num} - Option ${letter}`;
    }

    // Generic fallback
    return scenarioId.replace(/([a-z])(\d)/g, '$1 $2')
                   .replace(/scenario/i, 'Scenario')
                   .replace(/([a-z])([A-Z])/g, '$1 $2');
  };

  // Get hierarchical breadcrumb path
  const getBreadcrumbs = () => {
    const breadcrumbs = [];
    const current = currentScenario;

    // Always start with root
    breadcrumbs.push({
      id: 'root',
      title: getScenarioTitle('root')
    });

    // Add main scenario if we're in a sub-scenario
    if (current.match(/^scenario\d+[a-z]$/)) {
      const mainScenario = current.slice(0, -1);
      if (scenarioTree[mainScenario]) {
        breadcrumbs.push({
          id: mainScenario,
          title: getScenarioTitle(mainScenario)
        });
      }
    }

    // Add current scenario if it's not root
    if (current !== 'root') {
      breadcrumbs.push({
        id: current,
        title: getScenarioTitle(current)
      });
    }

    return breadcrumbs;
  };

  // Redirect to login if not authenticated
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-green-400 via-blue-400 to-purple-400">
        <div className="bg-white rounded-2xl p-8 shadow-2xl">
          <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-green-500 mx-auto"></div>
          <p className="text-center mt-4 text-gray-600 font-medium">Loading Story Tree...</p>
        </div>
      </div>
    );
  }

  if (!authLoading && !user) {
    router.push('/register');
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-green-400 via-blue-400 to-purple-400">
        <div className="bg-white rounded-2xl p-8 shadow-2xl text-center">
          <div className="animate-bounce mb-4">
            <TreePine className="w-16 h-16 text-green-500 mx-auto" />
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-t-4 border-b-4 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Building story branches...</p>
        </div>
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-400 via-pink-400 to-purple-400 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full text-center">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-3xl">🌳</span>
          </div>
          <h1 className="text-2xl font-bold text-red-600 mb-4">Story Tree Error</h1>
          <p className="text-gray-700 mb-6">{fetchError}</p>
          <div className="space-y-3">
            <button 
              onClick={() => window.location.reload()}
              className="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-xl transition-all transform hover:scale-105"
            >
              Try Again
            </button>
            <button 
              onClick={() => router.back()}
              className="w-full bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-xl transition-all transform hover:scale-105 flex items-center justify-center"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!storyData || (!storyData.storyText && (!storyData.questions || storyData.questions.length === 0))) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-400 via-blue-400 to-purple-400 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full text-center">
          <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <TreePine className="w-10 h-10 text-gray-400" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">No Story Tree Found</h1>
          <p className="text-gray-600 mb-6">This story adventure doesn't have any branches yet.</p>
          <button 
            onClick={() => router.back()}
            className="w-full bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-6 rounded-xl transition-all transform hover:scale-105 flex items-center justify-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const currentScenarioData = scenarioTree[currentScenario];

  return (
    <div className="min-h-screen bg-app-background">
      {/* Header */}
      <header className="bg-app-surface border-b border-app-surface-light px-4 sm:px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 sm:space-x-4">
            <button
              onClick={() => router.push(`/chat/${chatId}`)}
              className="flex items-center text-app-text-primary hover:text-app-text-secondary transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Back to Chat</span>
              <span className="sm:hidden">Back</span>
            </button>
            <div className="h-6 w-px bg-app-surface-light hidden sm:block"></div>
            <h1 className="text-lg sm:text-xl font-bold text-app-text-primary font-inter truncate">
              {chat?.title || 'Story Reader'}
            </h1>
          </div>

          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Story Level Indicator */}
            <div className="flex items-center bg-app-surface-light px-2 sm:px-3 py-2 rounded-md">
              <span className="text-app-text-secondary text-xs sm:text-sm font-medium">
                {currentScenario === 'root' && 'Main Menu'}
                {currentScenario.match(/^scenario\d+$/) && 'Main Scenario'}
                {currentScenario.match(/^scenario\d+[a-z]$/) && 'Sub-Scenario'}
              </span>
            </div>

            {/* Visited Counter */}
            <div className="flex items-center bg-app-surface-light px-2 sm:px-3 py-2 rounded-md">
              <MapPin className="w-4 h-4 text-app-accent mr-1 sm:mr-2" />
              <span className="text-app-text-secondary text-xs sm:text-sm font-medium">
                <span className="hidden sm:inline">{visitedScenarios.size} Visited</span>
                <span className="sm:hidden">{visitedScenarios.size}</span>
              </span>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 sm:px-6 py-6 sm:py-8 max-w-6xl">

        {/* Breadcrumb Navigation */}
        <div className="bg-app-surface rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
          <div className="flex items-center space-x-1 sm:space-x-2 overflow-x-auto">
            <Home className="w-4 h-4 text-app-text-secondary flex-shrink-0" />
            {getBreadcrumbs().map((crumb, index) => (
              <div key={crumb.id} className="flex items-center flex-shrink-0">
                {index > 0 && <ChevronRight className="w-4 h-4 text-app-text-secondary mx-1" />}
                <button
                  onClick={() => {
                    if (index < getBreadcrumbs().length - 1) {
                      setCurrentScenario(crumb.id);
                      // Update navigation history to reflect hierarchical navigation
                      const newHistory = ['root'];
                      if (crumb.id !== 'root') {
                        newHistory.push(crumb.id);
                      }
                      setNavigationHistory(newHistory);
                    }
                  }}
                  className={`text-xs sm:text-sm font-medium px-2 sm:px-3 py-1 rounded-md transition-colors font-inter whitespace-nowrap ${
                    index === getBreadcrumbs().length - 1
                      ? 'bg-app-accent text-app-text-primary'
                      : 'text-app-accent hover:bg-app-surface-light'
                  }`}
                >
                  {crumb.title}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Audio Control Panel */}
        <div className="bg-app-surface rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-app-text-primary flex items-center font-inter">
              <Volume2 className="w-5 h-5 mr-2 text-app-accent" />
              Audio Player
            </h2>
            <button
              onClick={() => setIsAudioEnabled(!isAudioEnabled)}
              className={`flex items-center px-4 py-2 rounded-md font-medium transition-colors font-inter ${
                isAudioEnabled
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-app-accent hover:bg-app-accent-hover text-app-text-primary'
              }`}
            >
              {isAudioEnabled ? <VolumeX className="w-4 h-4 mr-2" /> : <Volume2 className="w-4 h-4 mr-2" />}
              {isAudioEnabled ? 'Disable Audio' : 'Enable Audio'}
            </button>
          </div>

          {isAudioEnabled && (
            <div className="bg-app-surface-light p-4 rounded-md">
              <AudioPlayer
                text={getCurrentScenarioText()}
                voiceGender="FEMALE"
              />
            </div>
          )}
        </div>

        {/* Current Scenario */}
        <div className="bg-app-surface rounded-lg p-6 lg:p-8 mb-6">
          {/* Scenario Header */}
          <div className="text-center mb-6">
            <div className="inline-flex items-center bg-app-surface-light px-4 py-2 rounded-md mb-4">
              <Volume2 className="w-5 h-5 text-app-accent mr-2" />
              <span className="font-semibold text-app-text-primary font-inter">{getScenarioTitle(currentScenario)}</span>
            </div>
          </div>

          {/* Scenario Content */}
          <div className="bg-app-surface-light rounded-lg p-6 mb-6">
            <div className="bg-app-background rounded-md p-6">
              <div className="text-app-text-primary leading-relaxed text-base whitespace-pre-wrap font-inter">
                {currentScenarioData?.content || 'Loading scenario...'}
              </div>
            </div>
          </div>

          {/* Scenario Options */}
          {currentScenarioData?.options && currentScenarioData.options.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-app-text-primary text-center mb-4 font-inter">
                {currentScenario === 'root' ? 'Choose Your Story Path' : 'What happens next?'}
              </h3>
              <div className="grid gap-4 sm:grid-cols-2">
                {currentScenarioData.options.map((option, index) => (
                  <button
                    key={option.id}
                    onClick={() => navigateToScenario(option.target)}
                    className={`p-4 rounded-md text-left transition-colors border ${
                      visitedScenarios.has(option.target)
                        ? 'bg-app-surface-light border-app-accent text-app-text-primary'
                        : 'bg-app-background border-app-surface-light hover:border-app-accent text-app-text-primary hover:bg-app-surface-light'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-semibold text-app-text-primary mb-2 font-inter">
                          {currentScenario === 'root'
                            ? `Scenario ${index + 1}`
                            : `Option ${String.fromCharCode(65 + index)}`
                          }
                        </div>
                        <div className="text-app-text-secondary text-sm">
                          {option.text}
                        </div>
                      </div>
                      <div className="flex items-center ml-4">
                        {visitedScenarios.has(option.target) && (
                          <div className="w-5 h-5 bg-app-accent rounded-full flex items-center justify-center mr-2">
                            <span className="text-app-text-primary text-xs">✓</span>
                          </div>
                        )}
                        <ChevronRight className="w-4 h-4 text-app-text-secondary" />
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Navigation Controls */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-app-surface-light">
            <div className="flex items-center space-x-3">
              <button
                onClick={goBack}
                disabled={navigationHistory.length <= 1}
                className="flex items-center bg-app-surface-light hover:bg-app-surface-lighter disabled:opacity-50 disabled:cursor-not-allowed text-app-text-primary font-medium py-3 px-4 rounded-md transition-colors font-inter"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Go Back</span>
                <span className="sm:hidden">Back</span>
              </button>

              {/* Go to Main Menu - only show if not already at root */}
              {currentScenario !== 'root' && (
                <button
                  onClick={resetToRoot}
                  className="flex items-center bg-app-surface-light hover:bg-app-surface-lighter text-app-text-secondary font-medium py-3 px-4 rounded-md transition-colors font-inter"
                >
                  <Home className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Main Menu</span>
                  <span className="sm:hidden">Menu</span>
                </button>
              )}
            </div>

            <button
              onClick={resetToRoot}
              className="flex items-center bg-app-accent hover:bg-app-accent-hover text-app-text-primary font-semibold py-3 px-4 rounded-md transition-colors font-inter"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">Start Over</span>
              <span className="sm:hidden">Reset</span>
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center">
          <div className="bg-app-surface rounded-lg p-4">
            <p className="text-app-text-secondary font-medium flex items-center justify-center font-inter">
              <Volume2 className="w-4 h-4 mr-2 text-app-accent" />
              Navigate through your story by choosing different paths!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}